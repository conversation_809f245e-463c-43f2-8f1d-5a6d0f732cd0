import { DownloadState } from './types/types';

interface Env {
	MYBROWSER: Fetcher;
	NEXT_PUBLIC_REDIRECT_URL: string;
	PUPPETEER_MAX_DURATION?: string;
}

interface QueueJob {
	id: string;
	card: DownloadState;
	url: string;
	timestamp: number;
	status: 'pending' | 'processing' | 'completed' | 'failed';
	result?: ArrayBuffer;
	error?: string;
	startedAt?: number;
}

interface QueueStatus {
	size: number;
	pending: number;
	processing: number;
	isPaused: boolean;
}

interface AddJobRequest {
	card: DownloadState;
	url: string;
}

interface CompleteJobRequest {
	jobId: string;
	success: boolean;
	result?: number[];
	error?: string;
}

export class ImageQueue {
	private state: DurableObjectState;
	private jobs: Map<string, any> = new Map();
	private processing: number = 0;
	private initialized: boolean = false;
	private env: any;

	constructor(state: DurableObjectState, env: any) {
		this.state = state;
		this.env = env;
		// Initialize from storage on first access
		this.state.blockConcurrencyWhile(async () => {
			await this.initializeFromStorage();
		});
	}

	private async initializeFromStorage() {
		if (this.initialized) return;

		try {
			// Load jobs from storage
			const storedJobs = await this.state.storage.get("jobs");
			if (storedJobs) {
				this.jobs = new Map(Object.entries(storedJobs));
			}

			// Load processing count from storage
			const storedProcessing = await this.state.storage.get("processing");
			if (storedProcessing !== undefined) {
				this.processing = storedProcessing as number;
			}

			this.initialized = true;
		} catch (error) {
			console.error("Error initializing from storage:", error);
			this.initialized = true; // Continue even if storage fails
		}
	}

	private async saveToStorage() {
		try {
			// Convert Map to object for storage
			const jobsObject = Object.fromEntries(this.jobs);
			await this.state.storage.put({
				jobs: jobsObject,
				processing: this.processing
			});
		} catch (error) {
			console.error("Error saving to storage:", error);
		}
	}

	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);

		if (url.pathname === "/status") {
			return new Response(JSON.stringify({
				size: this.jobs.size,
				pending: this.jobs.size,
				processing: this.processing,
				isPaused: false
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// This mimics p-queue.add() behavior - it should return the actual result
		if (url.pathname === "/add" && request.method === "POST") {
			const { card, url: cardUrl, maxConcurrency } = await request.json();

			// If under concurrency limit, process immediately (like p-queue)
			if (this.processing < maxConcurrency) {
				console.log("Processing immediately (under concurrency limit)");
				this.processing++;
				await this.saveToStorage();

				try {
					// Actually generate the image here
					const { generateImage } = await import('./imageGeneration');
					const imageBuffer = await generateImage(cardUrl, card, this.env);

					this.processing--;
					await this.saveToStorage();

					console.log("Image generated immediately, size:", imageBuffer.byteLength);

					// Return the actual image (like p-queue resolving the Promise)
					return new Response(imageBuffer, {
						headers: {
							"Content-Type": "image/png",
							"Content-Length": imageBuffer.byteLength.toString()
						}
					});

				} catch (error) {
					console.error("Error generating image immediately:", error);
					this.processing--;
					await this.saveToStorage();

					return new Response(
						JSON.stringify({
							error: "Failed to generate card image. Please try again <NAME_EMAIL> if the issue persists.",
						}),
						{
							status: 500,
							headers: { "Content-Type": "application/json" }
						}
					);
				}
			} else {
				// Add to queue (like p-queue when at concurrency limit)
				console.log("Adding to queue (at concurrency limit)");
				const jobId = crypto.randomUUID();

				this.jobs.set(jobId, {
					id: jobId,
					card,
					url: cardUrl,
					createdAt: Date.now(),
					status: 'queued'
				});

				await this.saveToStorage();

				// Start processing the queue
				this.processQueue();

				// For now, return an error since we can't wait for the queued job in this context
				// In a real implementation, you'd need a different approach for queued jobs
				return new Response(
					JSON.stringify({
						error: "Server is at capacity. Please try again in a moment.",
					}),
					{
						status: 503,
						headers: { "Content-Type": "application/json" }
					}
				);
			}
		}

		// Process queued jobs
		if (url.pathname === "/process-queue" && request.method === "POST") {
			await this.processQueue();
			return new Response(JSON.stringify({ success: true }), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// Reset endpoint
		if (url.pathname === "/reset" && request.method === "POST") {
			this.jobs.clear();
			this.processing = 0;
			await this.state.storage.deleteAll();

			return new Response(JSON.stringify({
				success: true,
				message: "Queue reset successfully"
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		return new Response("Not found", { status: 404 });
	}

	private async processQueue() {
		while (this.processing < 1 && this.jobs.size > 0) {
			// Get the first queued job
			const queuedJob = Array.from(this.jobs.values()).find(job => job.status === 'queued');
			if (!queuedJob) break;

			// Mark as processing
			queuedJob.status = 'processing';
			this.processing++;
			await this.saveToStorage();

			try {
				// Actually generate the image
				const { generateImage } = await import('./imageGeneration');
				const imageBuffer = await generateImage(queuedJob.url, queuedJob.card, this.env);

				// Remove from jobs and decrement processing
				this.jobs.delete(queuedJob.id);
				this.processing--;
				await this.saveToStorage();

				console.log(`Queued job ${queuedJob.id} completed`);

			} catch (error) {
				console.error(`Queued job ${queuedJob.id} failed:`, error);
				this.jobs.delete(queuedJob.id);
				this.processing--;
				await this.saveToStorage();
			}
		}
	}
}
