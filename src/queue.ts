import { DownloadState } from './types/types';

interface Env {
	MYBROWSER: Fetcher;
	NEXT_PUBLIC_REDIRECT_URL: string;
	PUPPETEER_MAX_DURATION?: string;
}

interface QueueJob {
	id: string;
	card: DownloadState;
	url: string;
	timestamp: number;
	status: 'queued' | 'processing' | 'completed' | 'failed';
	result?: ArrayBuffer;
	error?: string;
	startedAt?: number;
	resolve?: (value: ArrayBuffer) => void;
	reject?: (error: Error) => void;
}

interface QueueStatus {
	size: number;
	pending: number;
	processing: number;
	isPaused: boolean;
}

interface AddJobRequest {
	card: DownloadState;
	url: string;
}

interface CompleteJobRequest {
	jobId: string;
	success: boolean;
	result?: number[];
	error?: string;
}

export class ImageQueue {
	private state: DurableObjectState;
	private jobs: Map<string, QueueJob> = new Map();
	private processing: number = 0;
	private initialized: boolean = false;
	private env: any;
	private pendingPromises: Map<string, { resolve: (value: <PERSON><PERSON><PERSON><PERSON>uffer) => void; reject: (error: Error) => void }> = new Map();
	private maxConcurrency: number = 10; // Default value, will be updated from requests

	constructor(state: DurableObjectState, env: any) {
		this.state = state;
		this.env = env;
		// Initialize from storage on first access
		this.state.blockConcurrencyWhile(async () => {
			await this.initializeFromStorage();
		});
	}

	private async initializeFromStorage() {
		if (this.initialized) return;

		try {
			// Load jobs from storage
			const storedJobs = await this.state.storage.get("jobs");
			if (storedJobs) {
				this.jobs = new Map(Object.entries(storedJobs));
			}

			// Load processing count from storage
			const storedProcessing = await this.state.storage.get("processing");
			if (storedProcessing !== undefined) {
				this.processing = storedProcessing as number;
			}

			// Load maxConcurrency from storage
			const storedMaxConcurrency = await this.state.storage.get("maxConcurrency");
			if (storedMaxConcurrency !== undefined) {
				this.maxConcurrency = storedMaxConcurrency as number;
			}

			console.log(`Initialized from storage: processing=${this.processing}, maxConcurrency=${this.maxConcurrency}, jobs=${this.jobs.size}`);
			this.initialized = true;
		} catch (error) {
			console.error("Error initializing from storage:", error);
			this.initialized = true; // Continue even if storage fails
		}
	}

	private async saveToStorage() {
		try {
			// Convert Map to object for storage
			const jobsObject = Object.fromEntries(this.jobs);
			await this.state.storage.put({
				jobs: jobsObject,
				processing: this.processing,
				maxConcurrency: this.maxConcurrency
			});
			console.log(`Saved to storage: processing=${this.processing}, maxConcurrency=${this.maxConcurrency}, jobs=${this.jobs.size}`);
		} catch (error) {
			console.error("Error saving to storage:", error);
		}
	}

	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);

		if (url.pathname === "/status") {
			const jobs = Array.from(this.jobs.values());
			const queuedJobs = jobs.filter(job => job.status === 'queued');
			const processingJobs = jobs.filter(job => job.status === 'processing');

			// Always sync processing count with actual processing jobs for consistency
			if (this.processing !== processingJobs.length) {
				console.warn(`Processing count mismatch: stored=${this.processing}, actual=${processingJobs.length}. Syncing...`);
				this.processing = processingJobs.length;
				await this.saveToStorage();
			}

			console.log(`Status check: ${queuedJobs.length} queued, ${processingJobs.length} processing, ${this.processing} processing count`);

			return new Response(JSON.stringify({
				size: queuedJobs.length, // Only count jobs waiting in queue
				pending: processingJobs.length, // Jobs currently being processed
				processing: processingJobs.length, // Same as pending for compatibility
				isPaused: false
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// This mimics p-queue.add() behavior - it should return the actual result
		if (url.pathname === "/add" && request.method === "POST") {
			const { card, url: cardUrl, maxConcurrency } = await request.json() as { card: any, url: string, maxConcurrency: number };

			// Store the maxConcurrency for this queue instance
			// Always use the latest value from the request, don't persist old values
			this.maxConcurrency = maxConcurrency || 10;
			console.log(`Updated maxConcurrency to: ${this.maxConcurrency}`);

			// Sync processing count with actual jobs before checking concurrency
			const jobs = Array.from(this.jobs.values());
			const actualProcessingJobs = jobs.filter(job => job.status === 'processing');
			if (this.processing !== actualProcessingJobs.length) {
				console.warn(`Before processing: syncing count from ${this.processing} to ${actualProcessingJobs.length}`);
				this.processing = actualProcessingJobs.length;
			}

			console.log(`Add request: processing=${this.processing}, maxConcurrency=${this.maxConcurrency}, canProcessImmediately=${this.processing < this.maxConcurrency}`);

			// If under concurrency limit, process immediately (like p-queue)
			if (this.processing < this.maxConcurrency) {
				console.log(`Processing immediately (under concurrency limit). Processing: ${this.processing}, Max: ${this.maxConcurrency}`);

				// Create a job record even for immediate processing to track it properly
				const jobId = crypto.randomUUID();
				const job: QueueJob = {
					id: jobId,
					card,
					url: cardUrl,
					timestamp: Date.now(),
					status: 'processing' // Mark as processing immediately
				};

				this.jobs.set(jobId, job);
				this.processing++;
				await this.saveToStorage();

				try {
					// Actually generate the image here
					const { generateImage } = await import('./imageGeneration');
					const imageBuffer = await generateImage(cardUrl, card, this.env);

					// Remove job and decrement processing
					this.jobs.delete(jobId);
					if (this.processing > 0) {
						this.processing--;
					}
					await this.saveToStorage();

					console.log("Image generated immediately, size:", imageBuffer.byteLength);

					// Return the actual image (like p-queue resolving the Promise)
					return new Response(imageBuffer, {
						headers: {
							"Content-Type": "image/png",
							"Content-Length": imageBuffer.byteLength.toString()
						}
					});

				} catch (error) {
					console.error("Error generating image immediately:", error);

					// Clean up job and processing count
					this.jobs.delete(jobId);
					if (this.processing > 0) {
						this.processing--;
					} else {
						console.error(`WARNING: Processing count was ${this.processing}, cannot decrement for immediate processing error`);
					}
					await this.saveToStorage();

					return new Response(
						JSON.stringify({
							error: "Failed to generate card image. Please try again <NAME_EMAIL> if the issue persists.",
						}),
						{
							status: 500,
							headers: { "Content-Type": "application/json" }
						}
					);
				}
			} else {
				// Add to queue and wait for completion (like p-queue)
				console.log(`Adding to queue (at concurrency limit). Processing: ${this.processing}, Max: ${this.maxConcurrency}`);
				const jobId = crypto.randomUUID();

				// Create a promise that will be resolved when the job completes
				return new Promise<Response>((resolve, reject) => {
					const job: QueueJob = {
						id: jobId,
						card,
						url: cardUrl,
						timestamp: Date.now(),
						status: 'queued'
					};

					this.jobs.set(jobId, job);
					this.pendingPromises.set(jobId, {
						resolve: (imageBuffer: ArrayBuffer) => {
							resolve(new Response(imageBuffer, {
								headers: {
									"Content-Type": "image/png",
									"Content-Length": imageBuffer.byteLength.toString()
								}
							}));
						},
						reject: (error: Error) => {
							resolve(new Response(
								JSON.stringify({
									error: "Failed to generate card image. Please try again <NAME_EMAIL> if the issue persists.",
								}),
								{
									status: 500,
									headers: { "Content-Type": "application/json" }
								}
							));
						}
					});

					// Save to storage and start processing
					this.saveToStorage().then(() => {
						this.processQueue(this.maxConcurrency);
					});
				});
			}
		}

		// Process queued jobs
		if (url.pathname === "/process-queue" && request.method === "POST") {
			console.log("Manual queue processing triggered");
			await this.processQueue(this.maxConcurrency);
			return new Response(JSON.stringify({
				success: true,
				processing: this.processing,
				queueSize: this.jobs.size,
				maxConcurrency: this.maxConcurrency
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// Reset endpoint
		if (url.pathname === "/reset" && request.method === "POST") {
			// Reject all pending promises
			for (const [jobId, promise] of this.pendingPromises) {
				promise.reject(new Error("Queue was reset"));
			}

			this.jobs.clear();
			this.pendingPromises.clear();
			this.processing = 0;
			await this.state.storage.deleteAll();

			return new Response(JSON.stringify({
				success: true,
				message: "Queue reset successfully"
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// Clean up orphaned jobs (jobs without pending promises)
		if (url.pathname === "/cleanup" && request.method === "POST") {
			let cleaned = 0;
			for (const [jobId, job] of this.jobs) {
				if (job.status === 'queued' && !this.pendingPromises.has(jobId)) {
					console.log(`Cleaning up orphaned job: ${jobId}`);
					this.jobs.delete(jobId);
					cleaned++;
				}
			}
			await this.saveToStorage();

			return new Response(JSON.stringify({
				success: true,
				message: `Cleaned up ${cleaned} orphaned jobs`
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// Fix processing count
		if (url.pathname === "/fix-processing" && request.method === "POST") {
			const jobs = Array.from(this.jobs.values());
			const processingJobs = jobs.filter(job => job.status === 'processing');
			const oldCount = this.processing;
			this.processing = processingJobs.length;
			await this.saveToStorage();

			return new Response(JSON.stringify({
				success: true,
				message: `Fixed processing count from ${oldCount} to ${this.processing}`,
				oldCount,
				newCount: this.processing,
				actualProcessingJobs: processingJobs.length
			}), {
				headers: { "Content-Type": "application/json" }
			});
		}

		// Debug endpoint
		if (url.pathname === "/debug") {
			const jobs = Array.from(this.jobs.values());
			const queuedJobs = jobs.filter(job => job.status === 'queued');
			const processingJobs = jobs.filter(job => job.status === 'processing');

			return new Response(JSON.stringify({
				processing: this.processing,
				maxConcurrency: this.maxConcurrency,
				totalJobs: this.jobs.size,
				queuedJobs: queuedJobs.length,
				processingJobs: processingJobs.length,
				pendingPromises: this.pendingPromises.size,
				jobs: jobs.map(job => ({
					id: job.id,
					status: job.status,
					timestamp: job.timestamp,
					hasPendingPromise: this.pendingPromises.has(job.id)
				}))
			}, null, 2), {
				headers: { "Content-Type": "application/json" }
			});
		}

		return new Response("Not found", { status: 404 });
	}

	private async processQueue(maxConcurrency?: number) {
		const concurrencyLimit = maxConcurrency || this.maxConcurrency;
		const queuedJobs = Array.from(this.jobs.values()).filter(job => job.status === 'queued');
		console.log(`processQueue called. Processing: ${this.processing}, Limit: ${concurrencyLimit}, Queued Jobs: ${queuedJobs.length}, Total Jobs: ${this.jobs.size}`);

		// Process jobs up to the concurrency limit
		while (this.processing < concurrencyLimit && queuedJobs.length > 0) {
			// Get the first queued job
			const queuedJob = queuedJobs.shift();
			if (!queuedJob) break;

			console.log(`Starting to process job ${queuedJob.id}`);
			// Mark as processing
			queuedJob.status = 'processing';
			this.processing++;
			await this.saveToStorage();

			// Process the job asynchronously (don't await here to allow concurrent processing)
			this.processJob(queuedJob).catch(error => {
				console.error(`Error in processJob for ${queuedJob.id}:`, error);
			});
		}

		console.log(`processQueue finished. Processing: ${this.processing}, Remaining queued: ${Array.from(this.jobs.values()).filter(job => job.status === 'queued').length}`);
	}

	private async processJob(job: QueueJob) {
		try {
			console.log(`Starting job ${job.id}`);
			// Actually generate the image
			const { generateImage } = await import('./imageGeneration');
			const imageBuffer = await generateImage(job.url, job.card, this.env);

			// Resolve the promise if it exists (for new jobs)
			const pendingPromise = this.pendingPromises.get(job.id);
			if (pendingPromise) {
				pendingPromise.resolve(imageBuffer);
				this.pendingPromises.delete(job.id);
			} else {
				// This is an orphaned job (loaded from storage without a promise)
				// We can't return the result to the client, so just log it
				console.log(`Completed orphaned job ${job.id} - no client waiting for result`);
			}

			// Remove from jobs and decrement processing (with safeguard)
			this.jobs.delete(job.id);
			if (this.processing > 0) {
				this.processing--;
			} else {
				console.error(`WARNING: Processing count was ${this.processing}, cannot decrement further for job ${job.id}`);
			}
			await this.saveToStorage();

			console.log(`Job ${job.id} completed. Processing: ${this.processing}, Queue size: ${this.jobs.size}`);

			// Continue processing the queue - use setTimeout to avoid blocking
			setTimeout(() => {
				this.processQueue();
			}, 0);

		} catch (error) {
			console.error(`Job ${job.id} failed:`, error);

			// Reject the promise if it exists (for new jobs)
			const pendingPromise = this.pendingPromises.get(job.id);
			if (pendingPromise) {
				pendingPromise.reject(error as Error);
				this.pendingPromises.delete(job.id);
			} else {
				// This is an orphaned job (loaded from storage without a promise)
				console.log(`Orphaned job ${job.id} failed - no client waiting for result`);
			}

			this.jobs.delete(job.id);
			if (this.processing > 0) {
				this.processing--;
			} else {
				console.error(`WARNING: Processing count was ${this.processing}, cannot decrement further for failed job ${job.id}`);
			}
			await this.saveToStorage();

			console.log(`Job ${job.id} failed. Processing: ${this.processing}, Queue size: ${this.jobs.size}`);

			// Continue processing the queue - use setTimeout to avoid blocking
			setTimeout(() => {
				this.processQueue();
			}, 0);
		}
	}
}
