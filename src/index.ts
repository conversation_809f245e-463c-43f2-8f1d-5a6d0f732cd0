import { jwtVerify } from 'jose';
import { ImageQueue } from './queue';
import { DownloadState } from './types/types';
import { generateImage } from './imageGeneration';
import getSubscriptionNameAndStatus from './getSubscriptionNameAndStatus';

interface Env {
	MYBROWSER: Fetcher;
	NEXT_PUBLIC_REDIRECT_URL: string;
	PUPPETEER_MAX_DURATION?: string;
	QUEUE?: DurableObjectNamespace;
	SUPABASE_JWT_SECRET: string;
	NEXT_PUBLIC_PROJECT_REF: string;
}

// Handle 10 concurrent requests as requested
const MAX_CONCURRENT_REQUESTS = 1;

function arrayBufferToBase64(buffer: ArrayBuffer): string {
	const bytes = new Uint8Array(buffer);
	const chunkSize = 8192;
	let binary = '';

	for (let i = 0; i < bytes.length; i += chunkSize) {
		const chunk = bytes.subarray(i, i + chunkSize);
		binary += String.fromCharCode(...chunk);
	}

	return btoa(binary);
}

const ALLOWED_ORIGINS = [
	"https://ultimatetcgcm.com",
	"https://dev.meguminrs.com",
	"https://preview.ultimatetcgcm.com",
	"https://www.ultimatetcgcm.com",
	"http://localhost:3000"
];

function createCORSHeaders(origin: string | null): Record<string, string> {
	const headers: Record<string, string> = {
		"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
		"Access-Control-Allow-Headers": "*, X-Request-Status-Only, Authorization, Content-Type",
		"Access-Control-Allow-Credentials": "true",
	};

	if (origin && ALLOWED_ORIGINS.includes(origin)) {
		headers["Access-Control-Allow-Origin"] = origin;
	}

	return headers;
}

async function verifySupabaseAccessTokenHS(token: string, jwtSecret: string, projectRef: string) {
	const enc = new TextEncoder();
	const ISSUER = `https://${projectRef}.supabase.co/auth/v1`;
	const { payload } = await jwtVerify(token, enc.encode(jwtSecret), {
		issuer: ISSUER,
		clockTolerance: 5,
	});
	return payload;
}

export default {
	async fetch(request: Request, env: Env): Promise<Response> {
		const url = new URL(request.url);
		const origin = request.headers.get("origin");

		console.log(`${request.method} ${url.pathname} from origin: ${origin}`);

		try {
			// Handle OPTIONS (preflight)
			if (request.method === "OPTIONS") {
				if (!origin || !ALLOWED_ORIGINS.includes(origin)) {
					return new Response(null, { status: 403 });
				}

				return new Response(null, {
					status: 200,
					headers: createCORSHeaders(origin),
				});
			}

			// Initialize queue Durable Object
			let queueStub: any = null;
			let useQueue = false;

			if (env.QUEUE) {
				try {
					const queueId = env.QUEUE.idFromName("image-queue");
					queueStub = env.QUEUE.get(queueId);
					useQueue = true;
				} catch (error) {
					console.warn("Failed to initialize Durable Objects queue:", error);
					useQueue = false;
				}
			}

			// Handle GET requests (queue status)
			if (request.method === "GET") {
				// Reset endpoint for testing
				if (url.pathname === "/reset" && useQueue) {
					try {
						await queueStub.fetch(new Request("http://internal/reset", {
							method: "POST"
						}));
						return new Response("Queue reset successfully", {
							status: 200,
							headers: createCORSHeaders(origin),
						});
					} catch (error) {
						console.error("Error resetting queue:", error);
						return new Response("Error resetting queue", {
							status: 500,
							headers: createCORSHeaders(origin),
						});
					}
				}

				// Test endpoint to check concurrency limit
				if (url.pathname === "/test-concurrency") {
					return new Response(JSON.stringify({
						MAX_CONCURRENT_REQUESTS,
						message: `Concurrency limit is set to ${MAX_CONCURRENT_REQUESTS}`
					}), {
						status: 200,
						headers: {
							"Content-Type": "application/json",
							...createCORSHeaders(origin),
						},
					});
				}

				// Manual trigger to process stuck queue
				if (url.pathname === "/process-queue" && useQueue) {
					try {
						const processResponse = await queueStub.fetch(new Request("http://internal/process-queue", {
							method: "POST"
						}));
						const result = await processResponse.json();
						return new Response(JSON.stringify(result), {
							status: 200,
							headers: {
								"Content-Type": "application/json",
								...createCORSHeaders(origin),
							},
						});
					} catch (error) {
						console.error("Error processing queue:", error);
						return new Response("Error processing queue", {
							status: 500,
							headers: createCORSHeaders(origin),
						});
					}
				}

				// Debug endpoint to see detailed queue state
				if (url.pathname === "/debug-queue" && useQueue) {
					try {
						const debugResponse = await queueStub.fetch(new Request("http://internal/debug"));
						const result = await debugResponse.json();
						return new Response(JSON.stringify(result, null, 2), {
							status: 200,
							headers: {
								"Content-Type": "application/json",
								...createCORSHeaders(origin),
							},
						});
					} catch (error) {
						console.error("Error getting debug info:", error);
						return new Response("Error getting debug info", {
							status: 500,
							headers: createCORSHeaders(origin),
						});
					}
				}

				// Cleanup orphaned jobs
				if (url.pathname === "/cleanup-queue" && useQueue) {
					try {
						const cleanupResponse = await queueStub.fetch(new Request("http://internal/cleanup", {
							method: "POST"
						}));
						const result = await cleanupResponse.json();
						return new Response(JSON.stringify(result), {
							status: 200,
							headers: {
								"Content-Type": "application/json",
								...createCORSHeaders(origin),
							},
						});
					} catch (error) {
						console.error("Error cleaning up queue:", error);
						return new Response("Error cleaning up queue", {
							status: 500,
							headers: createCORSHeaders(origin),
						});
					}
				}

				// Queue status
				if (useQueue) {
					try {
						const queueStatusResponse = await queueStub.fetch(new Request("http://internal/status"));
						const status = await queueStatusResponse.json();

						// Add queue position calculation for GET requests too
						const queueSize = status.size || 0;
						const pending = status.processing || 0;
						const atCapacity = pending >= MAX_CONCURRENT_REQUESTS;
						const queuePosition = atCapacity ? queueSize + 1 : 0;

						const enhancedStatus = {
							...status,
							queuePosition,
							estimatedTimeSeconds: queuePosition * 5,
						};

						return new Response(JSON.stringify(enhancedStatus), {
							status: 200,
							headers: {
								"Content-Type": "application/json",
								...createCORSHeaders(origin),
							},
						});
					} catch (error) {
						console.error("Error fetching queue status:", error);
					}
				}

				// Fallback queue status
				return new Response(JSON.stringify({
					size: 0,
					pending: 0,
					isPaused: false
				}), {
					status: 200,
					headers: {
						"Content-Type": "application/json",
						...createCORSHeaders(origin),
					},
				});
			}

			// Handle POST (image generation)
			if (request.method === "POST") {
				console.log("Processing POST request for image generation");

				// CORS check
				if (origin && !ALLOWED_ORIGINS.includes(origin)) {
					console.log("CORS rejected for origin:", origin);
					return new Response("Forbidden", { status: 403 });
				}

				// Auth check
				const authHeader = request.headers.get("Authorization");
				const token = authHeader?.split(" ")[1];

				if (!token) {
					console.log("No authorization token provided");
					return new Response("Unauthorized", {
						status: 401,
						headers: createCORSHeaders(origin),
					});
				}

				try {
					console.log("Verifying token...");
					await verifySupabaseAccessTokenHS(token, env.SUPABASE_JWT_SECRET, env.NEXT_PUBLIC_PROJECT_REF);
					console.log("Token verified, getting subscription...");

					const subscription = await getSubscriptionNameAndStatus(token);
					console.log("Subscription status:", subscription.status);

					// Parse request data (same as Next.js)
					const contentType = request.headers.get("content-type") || "";
					let card: DownloadState;

					console.log("Parsing request data, content-type:", contentType);

					if (contentType.includes("multipart/form-data")) {
						console.log("Processing multipart form data");
						const formData = await request.formData();
						const metadataString = formData.get("metadata");
						if (!metadataString || typeof metadataString !== "string") {
							console.error("Missing or invalid metadata in form data");
							return new Response("Missing metadata", {
								status: 400,
								headers: createCORSHeaders(origin),
							});
						}
						card = JSON.parse(metadataString) as DownloadState;

						// Handle uploaded image
						const imageFile = formData.get("image");
						if (imageFile && imageFile instanceof File) {
							console.log("Processing uploaded image");
							const arrayBuffer = await imageFile.arrayBuffer();
							const base64 = arrayBufferToBase64(arrayBuffer);
							card.imageUrl = `data:${imageFile.type};base64,${base64}`;
						}

						// Handle uploaded background image
						const backgroundImageFile = formData.get("backgroundImage");
						if (backgroundImageFile && backgroundImageFile instanceof File) {
							console.log("Processing uploaded background image");
							const arrayBuffer = await backgroundImageFile.arrayBuffer();
							const base64 = arrayBufferToBase64(arrayBuffer);
							card.backgroundImageUrl = `data:${backgroundImageFile.type};base64,${base64}`;
						}
					} else {
						console.log("Processing JSON data");
						card = await request.json() as DownloadState;
					}

					console.log("Card data parsed successfully");

					// Check for status-only request (same as Next.js)
					const wantsStatusInfo = request.headers.get("X-Request-Status-Only") === "true";

					if (wantsStatusInfo) {
						console.log("Returning status-only response");

						let queuePosition = 0;
						let queueSize = 0;
						let pending = 0;

						if (useQueue) {
							try {
								const queueStatusResponse = await queueStub.fetch(new Request("http://internal/status"));
								const status = await queueStatusResponse.json();
								queueSize = status.size || 0; // Number of queued jobs waiting
								pending = status.processing || 0; // Number of currently processing jobs

								// Queue position logic:
								// - If we can process immediately (not at capacity): position = 0
								// - If at capacity: position = number of jobs that need to complete before this one starts
								//   This includes: currently processing jobs that exceed available slots + queued jobs
								const atCapacity = pending >= MAX_CONCURRENT_REQUESTS;
								if (atCapacity) {
									// Position = queued jobs + 1 (this request will be queued)
									queuePosition = queueSize + 1;
								} else {
									// Can process immediately
									queuePosition = 0;
								}

								console.log(`Status check: queueSize=${queueSize}, pending=${pending}, atCapacity=${atCapacity}, position=${queuePosition}`);
							} catch (error) {
								console.error("Error getting queue status:", error);
							}
						}

						const estimatedTimeSeconds = queuePosition * 5;

						// Return 202 if there are processing jobs at capacity OR queued jobs waiting
						// This matches p-queue behavior: if you can't process immediately, you get queued
						const hasQueue = queueSize > 0;
						const wouldBeQueued = (pending >= MAX_CONCURRENT_REQUESTS) || hasQueue;

						if (wouldBeQueued) {
							return new Response(
								JSON.stringify({
									status: "generating",
									message: "Your card is being generated",
									queuePosition,
									estimatedTimeSeconds,
									queueSize,
									pending,
								}),
								{
									status: 202,
									headers: {
										"Content-Type": "application/json",
										...createCORSHeaders(origin),
									},
								}
							);
						} else {
							// Can process immediately
							return new Response(
								JSON.stringify({
									status: "ready",
									message: "Ready to process",
									queuePosition: 0,
									estimatedTimeSeconds: 0,
									queueSize: 0,
									pending,
								}),
								{
									status: 200,
									headers: {
										"Content-Type": "application/json",
										...createCORSHeaders(origin),
									},
								}
							);
						}
					}

					console.log("Building card URL...");
					// Build card URL (same as Next.js)
					const subStatus = subscription.status === "active";
					const cardColorArray = encodeURIComponent(JSON.stringify(card.colorArray));
					const abilityText = encodeURIComponent(card.ability.replace(/%/g, "%25"));
					const cardName = encodeURIComponent(card.name.replace(/%/g, "%25"));
					const donText = encodeURIComponent(card?.donText ? card?.donText.replace(/%/g, "%25") : "");
					const donPower = encodeURIComponent(card?.donPower ? card?.donPower.replace(/%/g, "%25") : "");
					const donFontSize = encodeURIComponent(card?.donFontSize || "");
					const cardType = encodeURIComponent(card.cardType.replace(/%/g, "%25"));
					const triggerText = encodeURIComponent(card.triggerText.replace(/%/g, "%25"));
					const cardSet = encodeURIComponent(card.set.replace(/%/g, "%25"));
					const cardRarity = encodeURIComponent(card.rarity.replace(/%/g, "%25"));
					const cardRarity2 = encodeURIComponent(card.rarity2.replace(/%/g, "%25"));
					const cardArtist = encodeURIComponent(card.artist.replace(/%/g, "%25"));
					const cardNum = encodeURIComponent(card.cardNum.replace(/%/g, "%25"));
					const counterText = encodeURIComponent(card.counterText.replace(/%/g, "%25"));
					const characterBorder = encodeURIComponent(card.characterBorder.replace(/%/g, "%25"));
					const cardLife = encodeURIComponent(card.life.replace(/%/g, "%25"));
					const cardLeaderBorder = encodeURIComponent(card.leaderBorder.replace(/%/g, "%25"));
					const cardEventBorder = encodeURIComponent(card?.eventBorder ? card.eventBorder.replace(/%/g, "%25") : "standard");
					const uuid = "9f84f1c0-1b57-4de7-942e-83e1ccf5c1b0";
					const cardUrl = `${env.NEXT_PUBLIC_REDIRECT_URL}/getCard/${card.cardKindRoute}?_vercel_share=LgCS3ImDMLj1RnGVkCmBBSvT1dYVtlz9&uuid=${uuid}&cardSubscriptionStatus=${subStatus}${cardColorArray ? `&colorArray=${cardColorArray}` : ""}&color=${card.color}&color2=${card.color2}&attribute=${card.attribute}${card.name ? `&name=${cardName}` : ""}${donText ? `&donText=${donText}` : ""}${donPower ? `&donPower=${donPower}` : ""}${donFontSize ? `&donFontSize=${donFontSize}` : ""}${card.cardType ? `&cardType=${cardType}` : ""}&cost=${card.cost}${card.power ? `&power=${card.power}` : ""}&abilityBackground=${card.abilityBackground}&trigger=${card.trigger}&counter=${card.counter}${card.counterText ? `&counterText=${counterText}` : ""}${card.set ? `&set=${cardSet}` : ""}${card.rarity ? `&rarity=${cardRarity}` : ""}${card.cardNum ? `&cardNum=${cardNum}` : ""}${card.printWave ? `&printWave=${card.printWave}` : ""}${card.donAbility ? `&donAbility=${card.donAbility}` : ""}${card.artist ? `&artist=${cardArtist}` : ""}${card.life ? `&life=${cardLife}` : ""}${card.rarity2 ? `&rarity2=${cardRarity2}` : ""}&foilBorder=${card.foilBorder}&dropShadow=${card.dropShadow}&abilityDropShadow=${card.abilityDropShadow}&abilityTextSize=${card.abilityTextSize}&blackBorder=${card.blackBorder}&rainbow=${card.rainbow}&powerBlack=${card.powerBlack}&leaderBorderEnabled=${card.leaderBorderEnabled}&aaStar=${card.aaStar}&leaderBorder=${cardLeaderBorder}&eventBorder=${cardEventBorder}&cardKindRoute=${card.cardKindRoute}&typeFontSize=${card.typeFontSize}&nameFontSize=${card.nameFontSize}&characterBorder=${characterBorder}&ability=${abilityText}&triggerText=${triggerText}&printReady=${card.printReady}&triggerTextFontSize=${card.triggerTextFontSize}&softwareAcceleration=true`;

					console.log("Card URL built, length:", cardUrl.length);

					// Use Durable Object queue to mimic p-queue behavior
					if (useQueue) {
						console.log("Adding task to Durable Object queue...");
						try {
							// This mimics queue.add() - it should return the actual image when the Promise resolves
							const addTaskResponse = await queueStub.fetch(new Request("http://internal/add", {
								method: "POST",
								body: JSON.stringify({
									card,
									url: cardUrl,
									maxConcurrency: MAX_CONCURRENT_REQUESTS
								}),
								headers: { "Content-Type": "application/json" }
							}));

							if (!addTaskResponse.ok) {
								throw new Error(`Queue error: ${addTaskResponse.status}`);
							}

							// The queue should handle the actual image generation and return the result
							// This mimics how p-queue.add() returns a Promise that resolves with the task result
							const imageBuffer = await addTaskResponse.arrayBuffer();
							console.log("Image generated via queue, size:", imageBuffer.byteLength);

							return new Response(imageBuffer, {
								status: 200,
								headers: {
									"Content-Type": "image/png",
									"Content-Length": imageBuffer.byteLength.toString(),
									...createCORSHeaders(origin),
								},
							});

						} catch (queueError) {
							console.error("Queue error, falling back to direct processing:", queueError);
							// Fall through to direct processing
						}
					}

					// Fallback: Process directly (same as Next.js when no queue)
					console.log("Processing image directly...");
					try {
						const imageBuffer = await generateImage(cardUrl, card, env);
						console.log("Image generated successfully, size:", imageBuffer.byteLength);

						return new Response(imageBuffer, {
							status: 200,
							headers: {
								"Content-Type": "image/png",
								"Content-Length": imageBuffer.byteLength.toString(),
								...createCORSHeaders(origin),
							},
						});
					} catch (imageError) {
						console.error("Error generating image:", imageError);

						return new Response(
							JSON.stringify({
								error: "Failed to generate card image. Please try again <NAME_EMAIL> if the issue persists.",
							}),
							{
								status: 500,
								headers: {
									"Content-Type": "application/json",
									...createCORSHeaders(origin),
								},
							}
						);
					}

				} catch (authError) {
					console.error("Authentication/authorization error:", authError);
					return new Response(
						JSON.stringify({
							error: "Authentication failed. Please check your credentials.",
						}),
						{
							status: 401,
							headers: {
								"Content-Type": "application/json",
								...createCORSHeaders(origin),
							},
						}
					);
				}
			}

			return new Response("Method not allowed", {
				status: 405,
				headers: createCORSHeaders(origin),
			});

		} catch (error) {
			console.error("Unhandled error in worker:", error);
			return new Response(
				JSON.stringify({
					error: "Internal server error. Please try again <NAME_EMAIL> if the issue persists.",
				}),
				{
					status: 500,
					headers: {
						"Content-Type": "application/json",
						...createCORSHeaders(origin),
					},
				}
			);
		}
	},
} satisfies ExportedHandler<Env>;

// Export the Durable Object
export { ImageQueue };
